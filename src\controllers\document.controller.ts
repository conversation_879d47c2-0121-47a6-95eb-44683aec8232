import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {SecurityBindings, UserProfile} from '@loopback/security';
import moment from 'moment';
import {v4 as uuidv4} from 'uuid';
import {Action, Document} from '../models';
import {ActionRepository, DocumentRepository, DocumentUserAllocationRepository, ServiceRepository, UserRepository} from '../repositories';
const SERVICE_NAME = "DOC"

export type docUpdate = {
  status: string,
  docId: string,
  actionId: string
}
export type docReturn = {
  status: string,
  docId: string,
  actionId: string,
  reason: string,
  from: string
}

@authenticate('cognito-jwt')
export class DocumentController {
  constructor(
    @repository(DocumentRepository)
    public documentRepository: DocumentRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(DocumentUserAllocationRepository)
    public documentUserAllocationRepository: DocumentUserAllocationRepository,
    @repository(ServiceRepository)
    public serviceRepository: ServiceRepository,
  ) { }

  @post('/documents')
  @response(200, {
    description: 'Document model instance',
    content: {'application/json': {schema: getModelSchemaRef(Document)}},
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Document, {
            title: 'NewDocument',
            exclude: ['id'],
          }),
        },
      },
    })
    document: Omit<Document, 'id'>,
  ): Promise<Document> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({where: {email: email}});
    if (!user) {
      throw new Error(`User not found with this email: ${email}`);
    }
    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})

    if (!service) {
      throw new Error('Service not found')
    }


    const count = await this.documentRepository.count();
    document.maskId = `DOC-${moment().format('YYMMDD')}-${count.count + 1}`;
    document.initiatorId = user.id ?? '';
    document.status = document.type === 'New' ? 'Sent to Curator' : 'Approved';

    const doc = await this.documentRepository.create(document);

    if (doc.type === 'New') {
      const action: Partial<Action> = {
        application: SERVICE_NAME,
        actionType: 'doc_initiated',
        actionToBeTaken: "Document Initiated",
        description: "Document Initiated",
        maskId: document.maskId,
        trackId: uuidv4(),
        sequence: '1',
        prefix: 'DOC',
        applicationId: doc.id,
        objectId: doc.id,
        dueDate: document.creatorTargetDate ?? undefined,
        submittedById: user.id ?? '',
        assignedToId: [document.creatorId],
        uploads: [],
        submitURL: '/document-submit',
        status: 'Initiated',
        serviceId: service.id,

      };
      const actionItem = await this.actionRepository.create(action);
    }





    return doc
  }

  @get('/my-documents')
  @response(200, {
    description: 'Array of Document model instances for the logged-in user',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Document, {includeRelations: true}),
        },
      },
    },
  })
  async findMyDocuments(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(Document) filter?: Filter<Document>,
  ): Promise<Document[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({where: {email: email}});
    if (!user) {
      throw new Error(`User not found with this email: ${email}`);
    }

    // Use the hasManyThrough relationship to get documents for this user
    return this.userRepository.documents(user.id!).find(filter);
  }

  @post('/documents/allocate-users')
  @response(200, {
    description: 'Allocate multiple users to multiple documents',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            message: {type: 'string'},
            allocationsCreated: {type: 'number'}
          }
        }
      }
    }
  })
  async allocateUsersToDocuments(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['userIds', 'documentIds'],
            properties: {
              userIds: {
                type: 'array',
                items: {type: 'string'},
                description: 'Array of user IDs to allocate'
              },
              documentIds: {
                type: 'array',
                items: {type: 'string'},
                description: 'Array of document IDs to allocate users to'
              }
            }
          }
        }
      }
    })
    allocationData: {
      userIds: string[];
      documentIds: string[];
    }
  ): Promise<{message: string; allocationsCreated: number}> {
    const {userIds, documentIds} = allocationData;

    if (!userIds || userIds.length === 0) {
      throw new Error('At least one user ID must be provided');
    }

    if (!documentIds || documentIds.length === 0) {
      throw new Error('At least one document ID must be provided');
    }

    // Verify current user exists
    const email = currentUserProfile.email;
    const currentUser = await this.userRepository.findOne({where: {email: email}});
    if (!currentUser) {
      throw new Error(`User not found with this email: ${email}`);
    }

    // Verify all users exist
    const users = await this.userRepository.find({where: {id: {inq: userIds}}});
    if (users.length !== userIds.length) {
      throw new Error('One or more user IDs are invalid');
    }

    // Verify all documents exist
    const documents = await this.documentRepository.find({where: {id: {inq: documentIds}}});
    if (documents.length !== documentIds.length) {
      throw new Error('One or more document IDs are invalid');
    }

    // Create allocations for each user-document combination
    const allocations = [];
    for (const userId of userIds) {
      for (const documentId of documentIds) {
        // Check if allocation already exists to avoid duplicates
        const existingAllocation = await this.documentUserAllocationRepository.findOne({
          where: {
            and: [
              {userId: userId},
              {documentId: documentId}
            ]
          }
        });

        if (!existingAllocation) {
          allocations.push({
            userId: userId,
            documentId: documentId
          });
        }
      }
    }

    // Bulk create allocations
    let allocationsCreated = 0;
    if (allocations.length > 0) {
      const createdAllocations = await this.documentUserAllocationRepository.createAll(allocations);
      allocationsCreated = createdAllocations.length;
    }

    return {
      message: `Successfully allocated ${userIds.length} users to ${documentIds.length} documents`,
      allocationsCreated: allocationsCreated
    };
  }

  @post('/documents/unallocate-users')
  @response(200, {
    description: 'Remove allocation of multiple users from multiple documents',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            message: {type: 'string'},
            allocationsRemoved: {type: 'number'}
          }
        }
      }
    }
  })
  async unallocateUsersFromDocuments(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['userIds', 'documentIds'],
            properties: {
              userIds: {
                type: 'array',
                items: {type: 'string'},
                description: 'Array of user IDs to unallocate'
              },
              documentIds: {
                type: 'array',
                items: {type: 'string'},
                description: 'Array of document IDs to unallocate users from'
              }
            }
          }
        }
      }
    })
    unallocationData: {
      userIds: string[];
      documentIds: string[];
    }
  ): Promise<{message: string; allocationsRemoved: number}> {
    const {userIds, documentIds} = unallocationData;

    if (!userIds || userIds.length === 0) {
      throw new Error('At least one user ID must be provided');
    }

    if (!documentIds || documentIds.length === 0) {
      throw new Error('At least one document ID must be provided');
    }

    // Verify current user exists
    const email = currentUserProfile.email;
    const currentUser = await this.userRepository.findOne({where: {email: email}});
    if (!currentUser) {
      throw new Error(`User not found with this email: ${email}`);
    }

    // Find existing allocations to remove
    const existingAllocations = await this.documentUserAllocationRepository.find({
      where: {
        and: [
          {userId: {inq: userIds}},
          {documentId: {inq: documentIds}}
        ]
      }
    });

    // Remove the allocations
    let allocationsRemoved = 0;
    if (existingAllocations.length > 0) {
      const allocationIds = existingAllocations.map(allocation => allocation.id!);
      await this.documentUserAllocationRepository.deleteAll({
        id: {inq: allocationIds}
      });
      allocationsRemoved = existingAllocations.length;
    }

    return {
      message: `Successfully unallocated ${userIds.length} users from ${documentIds.length} documents`,
      allocationsRemoved: allocationsRemoved
    };
  }

  @get('/documents/count')
  @response(200, {
    description: 'Document model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(Document) where?: Where<Document>,
  ): Promise<Count> {
    return this.documentRepository.count(where);
  }

  @get('/documents')
  @response(200, {
    description: 'Array of Document model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Document, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Document) filter?: Filter<Document>,
  ): Promise<Document[]> {
    return this.documentRepository.find(filter);
  }

  @patch('/documents')
  @response(200, {
    description: 'Document PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Document, {partial: true}),
        },
      },
    })
    document: Document,
    @param.where(Document) where?: Where<Document>,
  ): Promise<Count> {
    return this.documentRepository.updateAll(document, where);
  }

  @get('/documents/{id}')
  @response(200, {
    description: 'Document model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Document, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Document, {exclude: 'where'}) filter?: FilterExcludingWhere<Document>
  ): Promise<Document> {
    return this.documentRepository.findById(id, filter);
  }

  @patch('/curator-submit-documents/{actionId}')
  @response(204, {
    description: 'Document PATCH success',
  })
  async updateCuratorById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Document, {partial: true}),
        },
      },
    })
    document: Document,
  ): Promise<void> {

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({where: {email: email}});
    if (!user) {
      throw new Error(`User not found with this email: ${email}`);
    }

    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})
    if (!service) {
      throw new Error('Service not found')
    }

    const actionData = await this.actionRepository.findById(actionId)
    if (!actionData) {
      throw new Error('No Action Data Found')
    }

    const documentData = await this.documentRepository.findById(actionData.applicationId)
    if (!documentData) {
      throw new Error('No Inspection Data Found')
    }

    if (document.docStatus === 'In Draft') {

      await this.actionRepository.updateById(actionId, {status: 'In Progress'});
    }

    if (document.docStatus === 'Published') {
      const action: Partial<Action> = {
        application: SERVICE_NAME,
        actionType: 'perform_task',
        actionToBeTaken: "Review Document",
        description: "Review Document",
        maskId: documentData.maskId,
        trackId: actionData.trackId,
        sequence: '1',
        prefix: 'DOC',
        applicationId: documentData.id,
        objectId: documentData.id,
        dueDate: documentData.reviewerTargetDate ?? undefined,
        submittedById: user.id ?? '',
        assignedToId: [documentData.reviewerId],
        uploads: [],
        submitURL: '/document-task-submit',
        status: 'Initiated',
        serviceId: service.id,

      };
      await this.actionRepository.updateById(actionId, {status: 'Completed'});
      const actionItem = await this.actionRepository.create(action);
      document.status = 'In Review with Reviewer'
    }

    await this.documentRepository.updateById(documentData.id, document);
  }


  @patch('/curator-resubmit-documents/{actionId}')
  @response(204, {
    description: 'Document PATCH success',
  })
  async updateCuratorDocumentsById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Document, {partial: true}),
        },
      },
    })
    document: Document,
  ): Promise<void> {

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({where: {email: email}});
    if (!user) {
      throw new Error(`User not found with this email: ${email}`);
    }

    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})
    if (!service) {
      throw new Error('Service not found')
    }

    const actionData = await this.actionRepository.findById(actionId)
    if (!actionData) {
      throw new Error('No Action Data Found')
    }

    const documentData = await this.documentRepository.findById(actionData.applicationId)
    if (!documentData) {
      throw new Error('No Inspection Data Found')
    }

    if (document.docStatus === 'In Draft') {

      await this.actionRepository.updateById(actionId, {status: 'In Progress'});
    }

    if (document.docStatus === 'Published') {
      const action: Partial<Action> = {
        application: SERVICE_NAME,
        actionType: 'reperform_task',
        actionToBeTaken: "Review Document",
        description: "Review Document",
        maskId: documentData.maskId,
        trackId: actionData.trackId,
        sequence: '1',
        prefix: 'DOC',
        applicationId: documentData.id,
        objectId: documentData.id,
        dueDate: documentData.reviewerTargetDate ?? undefined,
        submittedById: user.id ?? '',
        assignedToId: [documentData.reviewerId],
        uploads: [],
        submitURL: '/document-task-submit',
        status: 'Initiated',
        serviceId: service.id,

      };
      await this.actionRepository.updateById(actionId, {status: 'Completed'});
      const actionItem = await this.actionRepository.create(action);
      document.status = 'In Review with Reviewer'
    }

    await this.documentRepository.updateById(documentData.id, document);
  }

  @patch('/document-task-submit/{actionId}')
  @response(204, {
    description: 'Inspection Task PATCH success',
  })
  async submitTaskByActionId(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Document, {partial: true}),
        },
      },
    })
    document: Document, // Removed optional marker
  ): Promise<void> {


    // Validate that taskData is provided

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({where: {email: email}});
    if (!user) {
      throw new Error(`User not found with this email: ${email}`);
    }

    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})
    if (!service) {
      throw new Error('Service not found')
    }

    const actionData = await this.actionRepository.findById(actionId)
    if (!actionData) {
      throw new Error('No Action Data Found')
    }

    const documentData = await this.documentRepository.findById(actionData.applicationId)
    if (!documentData) {
      throw new Error('No Inspection Data Found')
    }


    switch (actionData.actionType) {
      case 'perform_task': {

        if (!document.status || (document.status !== 'Returned' && document.status !== 'Completed')) {
          throw new Error('Method Type Status not allowed')
        }
        // Find a supervisor or manager to review the task
        // For now, we'll use the assignedById from the inspection data as the reviewer

        switch (document.status) {
          case 'Returned': {
            const actions: Partial<Action> = {
              application: SERVICE_NAME,
              actionType: 'doc_reinitiated',
              actionToBeTaken: 'Document Re-Initiated',
              actionTaken: 'Document Returned',
              uploads: actionData.uploads,
              evidence: actionData.evidence,
              description: actionData.description,
              comments: document.comments ?? '',
              maskId: documentData.maskId,
              trackId: actionData.trackId, // Generate unique id
              sequence: `${parseInt(actionData.sequence ?? '0') + 1}`,
              prefix: 'DOC',
              applicationId: documentData.id,
              dueDate: documentData.creatorTargetDate,
              objectId: documentData.id,
              submittedById: user.id,
              assignedToId: actionData.assignedToId, // Using the original assignedToId
              submitURL: '/document-task-submit',
              status: 'Initiated',
              serviceId: service.id,
              counter: actionData.counter ?? 0
            };


            // Insert into actionRepository
            await this.actionRepository.create(actions);

            // Send notification to the original assignee
            await this.documentRepository.updateById(documentData.id, {status: 'Returned to Creator'});
            break;
          }
          case 'Completed': {
            const actions: Partial<Action> = {
              application: SERVICE_NAME,
              actionType: 'verify_task',
              actionTaken: 'Document Reviewed',
              evidence: [],
              actionToBeTaken: 'Verify Document',
              description: actionData.description,
              maskId: documentData.maskId,
              trackId: actionData.trackId,
              uploads: actionData.uploads,
              sequence: '1',
              prefix: actionData.prefix,
              applicationId: documentData.id,
              objectId: documentData.id,
              dueDate: documentData.approverTargetDate,
              submittedById: user.id,
              assignedToId: [documentData.approverId ?? ''], // Using the assigned by ID as reviewer
              submitURL: '/document-task-submit',
              status: 'Initiated',
              serviceId: service.id,
              counter: actionData.counter ?? 0,
              comments: document.comments ?? ''

            };

            await this.actionRepository.updateById(actionId, {actionTaken: 'Document Reviewed', comments: document.comments ?? '', evidence: []});
            // Insert into actionRepository
            await this.actionRepository.create(actions);
            await this.documentRepository.updateById(documentData.id, {status: 'In Review with Approver'});

            break;
          }
        }

        break;
      }

      case 'reperform_task': {
        const actions: Partial<Action> = {
          application: SERVICE_NAME,
          actionType: 'verify_task',
          actionToBeTaken: 'Verify Document',
          actionTaken: 'Document Re Submitted',
          evidence: [],
          uploads: actionData.uploads,
          description: actionData.description,
          maskId: documentData.maskId,
          trackId: actionData.trackId,
          sequence: actionData.sequence,
          prefix: actionData.prefix,
          applicationId: documentData.id,
          objectId: documentData.id,
          submittedById: user.id,
          assignedToId: [documentData.approverId ?? ''], // Using the assigned by ID as reviewer
          submitURL: '/inspection-task-submit',
          dueDate: documentData.approverTargetDate,
          status: 'Initiated',
          serviceId: service.id,
          counter: actionData.counter ?? 0,
          comments: document.comments ?? ''
        };

        await this.actionRepository.updateById(actionId, {comments: document.comments ?? ''});
        // Insert into actionRepository
        await this.actionRepository.create(actions);
        await this.documentRepository.updateById(documentData.id, {status: 'In Review with Approver'});

        break;
      }

      case 'verify_task': {
        // Process the task verification
        if (!document.status || (document.status !== 'Returned' && document.status !== 'Completed')) {
          throw new Error('Method Type Status not allowed')
        }

        switch (document.status) {
          case 'Returned': {
            // For Returned status, use the original assignedToId from the action data
            const actions: Partial<Action> = {
              application: SERVICE_NAME,
              actionType: 'doc_reinitiated',
              actionToBeTaken: 'Document Re-Initiated',
              actionTaken: 'Document Returned',
              uploads: actionData.uploads,
              evidence: actionData.evidence,
              description: actionData.description,
              comments: document.comments ?? '',
              maskId: documentData.maskId,
              trackId: actionData.trackId, // Generate unique id
              sequence: `${parseInt(actionData.sequence ?? '0') + 1}`,
              prefix: 'DOC',
              applicationId: documentData.id,
              dueDate: documentData.creatorTargetDate,
              objectId: documentData.id,
              submittedById: user.id,
              assignedToId: actionData.assignedToId, // Using the original assignedToId
              submitURL: '/document-task-submit',
              status: 'Initiated',
              serviceId: service.id,
              counter: actionData.counter ?? 0
            };


            // Insert into actionRepository
            await this.actionRepository.create(actions);

            // Send notification to the original assignee
            await this.documentRepository.updateById(documentData.id, {status: 'Returned to Creator'});
            break;
          }

          case 'Completed': {
            // Check if there are any pending actions for this inspection

            // Send notification to the original assignee

            // Also notify the inspection creator
            await this.documentRepository.updateById(documentData.id, {status: 'Approved'});

            // status = pendingActions.count > 0 ? 'Completed with Actions' : 'Completed without Actions';
            break;
          }

          default:
            throw new Error('Method Type Status not allowed');
        }
        break;
      }

      default:
        throw new Error('Action type not allowed');
    }

    await this.actionRepository.updateById(actionId, {status: 'Completed', comments: document.comments ?? ''});

    // Update inspection with valid properties
    // const updateData: Partial<Inspection> = {

    // };

    // // Only add reviewerComments if it exists in the model
    // if (reviewerComments) {
    //   // Store it in the value field as a custom property
    //   updateData.value = {
    //     ...inspectionData.value,
    //     reviewerComments: reviewerComments
    //   };
    // }

    // await this.inspectionRepository.updateById(actionData.applicationId, updateData);
  }

  @patch('/documents/{id}')
  @response(204, {
    description: 'Document PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Document, {partial: true}),
        },
      },
    })
    document: Document,
  ): Promise<void> {
    await this.documentRepository.updateById(id, document);
  }

  @put('/documents/{id}')
  @response(204, {
    description: 'Document PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() document: Document,
  ): Promise<void> {
    await this.documentRepository.replaceById(id, document);
  }

  @del('/documents/{id}')
  @response(204, {
    description: 'Document DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.documentRepository.deleteById(id);
  }
}
